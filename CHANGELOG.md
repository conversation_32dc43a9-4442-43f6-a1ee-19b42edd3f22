# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Fixed
- **Variabelväljaren visar nu alla variabler korrekt** - Fixade problem där steg som använde default-variabelnamn inte visades i variabelväljaren. Frontend använder nu samma logik som backend för variabeldetektering (`step.variableName || getDefaultVariableName(step.type, stepIndex)`).
  - Påverkade komponenter: `VariableHelper.tsx`, `VariablesModal.tsx`
  - Nu visas automatiskt genererade variabelnamn som `extractedText_1`, `screenshot_2`, etc.
  - Konsistent beteende mellan frontend och backend

### Documentation
- Uppdaterade `docs/development/architecture.md` med detaljerad information om variabelhantering
- Lade till troubleshooting-sektion för variabelväljarproblem i `docs/development/troubleshooting.md`
- Dokumenterade alla variabelskapande stegtyper och deras default-namn

## [1.1.0] - 2024-XX-XX

### Added
- Initial RPA flow designer implementation
- React Flow integration for visual flow editing
- Step-based RPA automation system
- Playwright runner for browser automation
- AI runner for PDF processing and LLM integration
- API runner for external service integration
- Variable system for data flow between steps
- Credential management with encryption
- OAuth2 token management for external APIs
- Scheduling system for automated flow execution
- Real-time execution logging and monitoring

### Features
- **Flow Designer**: Visual drag-and-drop interface for creating RPA flows
- **Step Library**: Comprehensive set of automation steps (navigation, interaction, extraction, AI, API)
- **Variable Management**: Dynamic variable creation and usage across steps
- **Credential Security**: Encrypted storage of usernames, passwords, and API tokens
- **Multi-Runner Architecture**: Pluggable runner system for different automation types
- **Scheduling**: Cron-based scheduling for automated flow execution
- **Logging**: Detailed execution logs with color-coded status indicators
- **Customer Management**: Multi-tenant support with customer-specific credentials

### Technical
- **Frontend**: React + TypeScript + Vite + React Flow
- **Backend**: Node.js + TypeScript + Express + Playwright
- **Database**: Better-SQLite3 for local development
- **Queue System**: BullMQ + Redis for job processing
- **AI Integration**: OpenAI API for intelligent document processing
- **API Integration**: Fortnox, Visma, eEkonomi support

## [1.0.0] - 2024-XX-XX

### Added
- Initial project setup and architecture
- Basic RPA framework foundation
- Development environment configuration
