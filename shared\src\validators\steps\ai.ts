import { RpaStep, ExtractPdfValuesStep } from '../../types/steps';
import { ValidationResult, ValidationError, createStepFromType } from '../../utils';

export function validateAiStep(step: RpaStep): ValidationResult {
  const errors: ValidationError[] = [];

  switch (step.type) {
    case 'extractPdfValues':
      const pdfStep = step as ExtractPdfValuesStep;

      if (!pdfStep.base64Input || pdfStep.base64Input.trim() === '') {
        errors.push({
          field: 'base64Input',
          message: 'Base64 input is required for PDF values extraction',

        });
      }

      if (!pdfStep.prompt || pdfStep.prompt.trim() === '') {
        errors.push({
          field: 'prompt',
          message: 'Prompt is required for PDF values extraction',

        });
      }

      if (!pdfStep.variableName || pdfStep.variableName.trim() === '') {
        errors.push({
          field: 'variableName',
          message: 'Variable name is required for PDF values extraction',

        });
      }

      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function createAiStepFromType(stepType: string): RpaStep {
  switch (stepType) {
    case 'extractPdfText':
      return createStepFromType(stepType);
    default:
      throw new Error(`Not an AI step type: ${stepType}`);
  }
}
