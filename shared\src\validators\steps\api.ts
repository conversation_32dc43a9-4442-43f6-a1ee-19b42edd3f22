import { RpaStep } from '../../types';
import { ValidationResult, ValidationError } from '../../utils';
import type { FortnoxCreateVoucherStep } from '../../types/steps/api';

/**
 * Validerar Fortnox Create Voucher steg
 */
export function validateFortnoxCreateVoucherStep(step: FortnoxCreateVoucherStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Grundläggande validering
  if (!step.type || step.type !== 'fortnoxCreateVoucher') {
    errors.push({
      field: 'type',
      message: 'Steg-typ måste vara "fortnoxCreateVoucher"',

    });
  }

  if (!step.name?.trim()) {
    errors.push({
      field: 'name',
      message: 'Namn är obligatoriskt',

    });
  }

  // Validera input-variabel
  if (!step.inputVariable?.trim()) {
    errors.push({
      field: 'inputVariable',
      message: 'Input-variabel är obligatorisk',

    });
  }

  // Validera AI-prompt
  if (!step.aiPrompt?.trim()) {
    errors.push({
      field: 'aiPrompt',
      message: 'AI-prompt är obligatorisk',

    });
  }

  // Validera verifikationsserie
  if (step.voucherSeries && !/^[A-Z]$/.test(step.voucherSeries)) {
    errors.push({
      field: 'voucherSeries',
      message: 'Verifikationsserie måste vara en stor bokstav (A-Z)',

    });
  }

  // Validera transaktionsdatum format (om angivet)
  if (step.transactionDate && !isValidDateFormat(step.transactionDate)) {
    errors.push({
      field: 'transactionDate',
      message: 'Transaktionsdatum måste vara i format YYYY-MM-DD eller en variabel',

    });
  }

  // Validera variabelnamn
  if (step.variableName && !/^[a-zA-Z][a-zA-Z0-9_-]*$/.test(step.variableName)) {
    errors.push({
      field: 'variableName',
      message: 'Variabelnamn måste börja med en bokstav och får endast innehålla bokstäver, siffror, bindestreck och understreck',

    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}



/**
 * Kontrollerar om datum är i giltigt format (YYYY-MM-DD eller variabel)
 */
function isValidDateFormat(date: string): boolean {
  // Tillåt variabler (börjar med ${)
  if (date.startsWith('${') && date.endsWith('}')) {
    return true;
  }

  // Kontrollera YYYY-MM-DD format
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(date)) {
    return false;
  }

  // Kontrollera att det är ett giltigt datum
  const parsedDate = new Date(date);
  return parsedDate instanceof Date && !isNaN(parsedDate.getTime());
}

/**
 * Skapar ett nytt Fortnox Create Voucher steg med standardvärden
 */
export function createFortnoxCreateVoucherStep(): FortnoxCreateVoucherStep {
  return {
    id: '',
    type: 'fortnoxCreateVoucher',
    name: 'Skapa Fortnox Verifikation',
    description: 'Skapa verifikation i Fortnox med AI',
    inputVariable: '',
    aiPrompt: 'Skapa en balanserad verifikation baserat på input-data enligt svensk bokföringssed',
    voucherSeries: 'A',
    variableName: 'var-fortnox-voucher'
  };
}

// Huvudvalidator för API-steg
export function validateApiStep(step: RpaStep): ValidationResult {
  switch ((step as any).type) {
    case 'fortnoxCreateVoucher':
      return validateFortnoxCreateVoucherStep(step as unknown as FortnoxCreateVoucherStep);
    default:
      return {
        isValid: false,
        errors: [{
          field: 'type',
          message: `Okänd API steg-typ: ${step.type}`,
          
        }]
      };
  }
}

export function createApiStepFromType(stepType: string): any {
  switch (stepType) {
    case 'fortnoxCreateVoucher':
      return createFortnoxCreateVoucherStep();
    default:
      throw new Error(`Okänd API steg-typ: ${stepType}`);
  }
}
